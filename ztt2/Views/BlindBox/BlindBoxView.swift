//
//  BlindBoxView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒主页面
 * 集成所有盲盒相关组件，提供完整的盲盒功能界面
 */
struct BlindBoxView: View {
    
    // MARK: - Properties
    @StateObject private var viewModel: BlindBoxViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    
    let member: Member
    let onDismiss: () -> Void
    let onNavigateToSettings: (() -> Void)?
    
    // MARK: - Animation State
    @State private var pageAppeared = false
    @State private var showBlindBoxConfigPopup = false
    
    // MARK: - Initialization
    
    init(
        member: Member,
        onDismiss: @escaping () -> Void,
        onNavigateToSettings: (() -> Void)? = nil
    ) {
        self.member = member
        self.onDismiss = onDismiss
        self.onNavigateToSettings = onNavigateToSettings
        self._viewModel = StateObject(wrappedValue: BlindBoxViewModel(member: member))
    }
    
    var body: some View {
        ZStack {
            // 背景渐变
            backgroundGradient
                .ignoresSafeArea()
            
            // 主要内容
            VStack(spacing: 0) {
                // 导航栏
                navigationBar
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : -20)
                    .animation(.easeOut(duration: 0.6), value: pageAppeared)
                
                // 内容区域
                contentArea
            }
            
            // 粒子效果层
            ParticleEffectView(viewModel: viewModel)
                .allowsHitTesting(false)
            
            // 结果弹窗
            if viewModel.showResult {
                resultOverlay
            }
            
            // 积分不足提示
            if viewModel.showInsufficientPoints {
                insufficientPointsAlert
            }
        }
        .onAppear {
            viewModel.loadBlindBoxConfig()
            withAnimation(.easeOut(duration: 0.8)) {
                pageAppeared = true
            }
        }
        .onDisappear {
            pageAppeared = false
        }
        // 盲盒配置弹窗
        .overlay(
            BlindBoxConfigPopupView(
                isPresented: $showBlindBoxConfigPopup,
                selectedMember: member,
                onSave: { configData in
                    handleBlindBoxConfigSave(configData)
                },
                onCancel: {
                    showBlindBoxConfigPopup = false
                }
            )
        )
    }
    
    // MARK: - Background
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primary.opacity(0.1),
                DesignSystem.Colors.secondary.opacity(0.05),
                Color.white
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - Navigation Bar
    
    private var navigationBar: some View {
        HStack {
            // 返回按钮
            Button(action: {
                dismiss()
                onDismiss()
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text("blind_box.back_button".localized)
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            
            Spacer()
            
            // 标题
            Text("blind_box.title".localized)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
            
            // 占位符保持布局平衡（移除设置按钮）
            Color.clear
                .frame(width: 24, height: 24)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
    }
    
    // MARK: - Content Area
    
    private var contentArea: some View {
        VStack(spacing: 20) {
            if viewModel.isLoading {
                loadingView
            } else if viewModel.showNoConfig {
                BlindBoxEmptyStateView {
                    showBlindBoxConfigPopup = true
                }
            } else {
                // 统计信息
                BlindBoxStatsView(viewModel: viewModel)
                    .padding(.horizontal, 20)
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 30)
                    .animation(.easeOut(duration: 0.8).delay(0.2), value: pageAppeared)
                
                // 盲盒网格
                BlindBoxGridView(viewModel: viewModel) { index in
                    handleBoxTapped(at: index)
                }
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 40)
                .animation(.easeOut(duration: 0.8).delay(0.4), value: pageAppeared)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(DesignSystem.Colors.primary)
            
            Text("blind_box.loading".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Result Overlay
    
    private var resultOverlay: some View {
        BlindBoxResultView(
            prizeName: viewModel.resultPrize,
            costPoints: viewModel.costPerOpen,
            onConfirm: {
                viewModel.confirmResult()
            },
            onCancel: {
                viewModel.showResult = false
                viewModel.resultPrize = ""
            }
        )
        .zIndex(1000)
    }
    
    // MARK: - Insufficient Points Alert
    
    private var insufficientPointsAlert: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    viewModel.showInsufficientPoints = false
                }
            
            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundColor(.orange)
                
                Text("blind_box.error.insufficient_points_title".localized)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(String(format: "blind_box.error.insufficient_points_message".localized, viewModel.costPerOpen, Int(member.currentPoints)))
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                
                Button(action: {
                    viewModel.showInsufficientPoints = false
                }) {
                    Text("blind_box.error.understand".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.orange)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
        }
    }
    
    // MARK: - Event Handlers
    
    /**
     * 处理盲盒点击事件
     */
    private func handleBoxTapped(at index: Int) {
        let success = viewModel.openBlindBox(at: index)
        if success {
            print("✅ 盲盒开启成功，索引: \(index)")
        } else {
            print("❌ 盲盒开启失败，索引: \(index)")
        }
    }

    /**
     * 处理盲盒配置保存
     */
    private func handleBlindBoxConfigSave(_ configData: BlindBoxConfigData) {
        print("保存盲盒配置: 成员=\(member.name ?? "未知"), 盲盒数=\(configData.boxCount), 积分=\(configData.costPerPlay)")
        print("奖品列表: \(configData.boxPrizes)")

        // 调用DataManager保存盲盒配置
        let savedConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: configData.boxCount,
            costPerPlay: configData.costPerPlay,
            boxPrizes: configData.boxPrizes
        )

        // 关闭弹窗
        showBlindBoxConfigPopup = false

        // 根据保存结果重新加载配置
        if savedConfig != nil {
            // 保存成功，重新加载盲盒配置
            viewModel.loadBlindBoxConfig()
            print("✅ 盲盒配置保存成功，已重新加载")
        } else {
            print("❌ 盲盒配置保存失败")
        }
    }
}

#Preview {
    // 简化的预览，避免Core Data初始化问题
    VStack {
        Text("盲盒主页面预览")
            .font(.title)
            .padding()

        Text("请在实际应用中查看完整功能")
            .font(.caption)
            .foregroundColor(.secondary)

        Text("通过成员详情页 → 抽奖 → 盲盒 进入")
            .font(.caption2)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(
        LinearGradient(
            colors: [
                DesignSystem.Colors.primary.opacity(0.1),
                DesignSystem.Colors.secondary.opacity(0.05),
                Color.white
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
}
