//
//  ParticleSystemView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import SwiftUI

/**
 * 粒子系统组件
 * 实现各种粒子效果，包括庆祝、爆炸、火花等
 */
struct ParticleSystemView: View {
    
    // MARK: - Properties
    let particles: [ParticleItem]
    let isActive: Bool
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                particleView(particle: particle)
            }
        }
        .allowsHitTesting(false) // 粒子不接收触摸事件
    }
    
    // MARK: - Particle View
    
    private func particleView(particle: ParticleItem) -> some View {
        Circle()
            .fill(particle.color)
            .frame(width: particle.size, height: particle.size)
            .position(particle.currentPosition)
            .opacity(particle.opacity)
            .scaleEffect(particle.opacity) // 根据生命周期缩放
            .blur(radius: (1 - particle.opacity) * 2) // 死亡时模糊
    }
}

/**
 * 高级粒子系统组件
 * 支持更复杂的粒子效果和自定义行为
 */
struct AdvancedParticleSystemView: View {
    
    // MARK: - Properties
    @ObservedObject var particleSystem: ParticleSystem
    
    var body: some View {
        TimelineView(.animation) { timeline in
            ZStack {
                ForEach(particleSystem.particles) { particle in
                    advancedParticleView(particle: particle, time: timeline.date)
                }
            }
            .allowsHitTesting(false)
        }
        .onAppear {
            particleSystem.start()
        }
        .onDisappear {
            particleSystem.stop()
        }
    }
    
    // MARK: - Advanced Particle View
    
    private func advancedParticleView(particle: AdvancedParticle, time: Date) -> some View {
        Group {
            switch particle.type {
            case .circle:
                Circle()
                    .fill(particle.color)
            case .star:
                StarShape()
                    .fill(particle.color)
            case .heart:
                HeartShape()
                    .fill(particle.color)
            case .sparkle:
                SparkleShape()
                    .fill(particle.color)
            }
        }
        .frame(width: particle.size, height: particle.size)
        .position(particle.position)
        .opacity(particle.opacity)
        .scaleEffect(particle.scale)
        .rotationEffect(.degrees(particle.rotation))
        .blur(radius: particle.blurRadius)
    }
}

// MARK: - Particle System Manager

/**
 * 粒子系统管理器
 * 负责粒子的生成、更新和销毁
 */
class ParticleSystem: ObservableObject {
    
    // MARK: - Published Properties
    @Published var particles: [AdvancedParticle] = []
    
    // MARK: - Private Properties
    private var emitter: ParticleEmitter
    private var timer: Timer?
    private var isRunning = false
    
    // MARK: - Initialization
    
    init(emitter: ParticleEmitter) {
        self.emitter = emitter
    }
    
    // MARK: - Public Methods
    
    /**
     * 开始粒子系统
     */
    func start() {
        guard !isRunning else { return }
        isRunning = true
        
        timer = Timer.scheduledTimer(withTimeInterval: 1/60.0, repeats: true) { _ in
            self.update()
        }
    }
    
    /**
     * 停止粒子系统
     */
    func stop() {
        isRunning = false
        timer?.invalidate()
        timer = nil
        particles.removeAll()
    }
    
    /**
     * 手动发射粒子
     */
    func emit(at position: CGPoint, count: Int = -1) {
        let particleCount = count > 0 ? count : emitter.emissionRate
        let newParticles = (0..<particleCount).map { _ in
            emitter.createParticle(at: position)
        }
        particles.append(contentsOf: newParticles)
    }
    
    // MARK: - Private Methods
    
    /**
     * 更新粒子系统
     */
    private func update() {
        let deltaTime = 1.0 / 60.0
        
        // 更新现有粒子
        for i in particles.indices {
            particles[i].update(deltaTime: deltaTime)
        }
        
        // 移除死亡的粒子
        particles.removeAll { !$0.isAlive }
        
        // 根据发射器配置生成新粒子
        if emitter.continuousEmission && particles.count < emitter.maxParticles {
            let newParticles = (0..<emitter.emissionRate).map { _ in
                emitter.createParticle(at: emitter.emissionPosition)
            }
            particles.append(contentsOf: newParticles)
        }
    }
}

// MARK: - Particle Types

/**
 * 粒子类型枚举
 */
enum ParticleType: CaseIterable {
    case circle
    case star
    case heart
    case sparkle
}

// MARK: - Particle Emitter

/**
 * 粒子发射器
 * 定义粒子的生成规则和属性
 */
struct ParticleEmitter {
    
    // MARK: - Emission Properties
    let emissionPosition: CGPoint
    let emissionRate: Int
    let maxParticles: Int
    let continuousEmission: Bool
    
    // MARK: - Particle Properties
    let particleType: ParticleType
    let colors: [Color]
    let sizeRange: ClosedRange<CGFloat>
    let velocityRange: ClosedRange<CGFloat>
    let angleRange: ClosedRange<Double>
    let lifespanRange: ClosedRange<Double>
    
    // MARK: - Physics Properties
    let gravity: CGFloat
    let airResistance: CGFloat
    
    // MARK: - Visual Properties
    let fadeIn: Bool
    let fadeOut: Bool
    let scaleOverLife: Bool
    let rotationSpeed: Double
    
    // MARK: - Initialization
    
    init(
        emissionPosition: CGPoint = .zero,
        emissionRate: Int = 10,
        maxParticles: Int = 100,
        continuousEmission: Bool = false,
        particleType: ParticleType = .circle,
        colors: [Color] = [.white],
        sizeRange: ClosedRange<CGFloat> = 4...8,
        velocityRange: ClosedRange<CGFloat> = 50...150,
        angleRange: ClosedRange<Double> = 0...(2 * .pi),
        lifespanRange: ClosedRange<Double> = 1...3,
        gravity: CGFloat = 100,
        airResistance: CGFloat = 0.98,
        fadeIn: Bool = false,
        fadeOut: Bool = true,
        scaleOverLife: Bool = false,
        rotationSpeed: Double = 0
    ) {
        self.emissionPosition = emissionPosition
        self.emissionRate = emissionRate
        self.maxParticles = maxParticles
        self.continuousEmission = continuousEmission
        self.particleType = particleType
        self.colors = colors
        self.sizeRange = sizeRange
        self.velocityRange = velocityRange
        self.angleRange = angleRange
        self.lifespanRange = lifespanRange
        self.gravity = gravity
        self.airResistance = airResistance
        self.fadeIn = fadeIn
        self.fadeOut = fadeOut
        self.scaleOverLife = scaleOverLife
        self.rotationSpeed = rotationSpeed
    }
    
    // MARK: - Particle Creation
    
    /**
     * 创建新粒子
     */
    func createParticle(at position: CGPoint) -> AdvancedParticle {
        let angle = Double.random(in: angleRange)
        let velocity = CGFloat.random(in: velocityRange)
        let color = colors.randomElement() ?? .white
        let size = CGFloat.random(in: sizeRange)
        let lifespan = Double.random(in: lifespanRange)
        
        return AdvancedParticle(
            type: particleType,
            position: position,
            velocity: CGPoint(
                x: Foundation.cos(angle) * velocity,
                y: Foundation.sin(angle) * velocity
            ),
            color: color,
            size: size,
            lifespan: lifespan,
            gravity: gravity,
            airResistance: airResistance,
            fadeIn: fadeIn,
            fadeOut: fadeOut,
            scaleOverLife: scaleOverLife,
            rotationSpeed: rotationSpeed
        )
    }
}

// MARK: - Advanced Particle

/**
 * 高级粒子数据模型
 */
struct AdvancedParticle: Identifiable {

    let id = UUID()

    // MARK: - Basic Properties
    let type: ParticleType
    var position: CGPoint
    var velocity: CGPoint
    let color: Color
    let size: CGFloat

    // MARK: - Life Properties
    let lifespan: Double
    var age: Double = 0

    // MARK: - Physics Properties
    let gravity: CGFloat
    let airResistance: CGFloat

    // MARK: - Visual Properties
    let fadeIn: Bool
    let fadeOut: Bool
    let scaleOverLife: Bool
    let rotationSpeed: Double
    var rotation: Double = 0

    // MARK: - Computed Properties

    var isAlive: Bool {
        return age < lifespan
    }

    var lifeProgress: Double {
        return age / lifespan
    }

    var opacity: Double {
        if fadeIn && lifeProgress < 0.1 {
            return lifeProgress / 0.1
        } else if fadeOut && lifeProgress > 0.7 {
            return 1.0 - (lifeProgress - 0.7) / 0.3
        } else {
            return 1.0
        }
    }

    var scale: Double {
        if scaleOverLife {
            return 1.0 - lifeProgress * 0.5
        } else {
            return 1.0
        }
    }

    var blurRadius: Double {
        return lifeProgress * 2
    }

    // MARK: - Update Method

    /**
     * 更新粒子状态
     */
    mutating func update(deltaTime: Double) {
        age += deltaTime

        // 更新位置
        position.x += velocity.x * CGFloat(deltaTime)
        position.y += velocity.y * CGFloat(deltaTime)

        // 应用重力
        velocity.y += gravity * CGFloat(deltaTime)

        // 应用空气阻力
        velocity.x *= airResistance
        velocity.y *= airResistance

        // 更新旋转
        rotation += rotationSpeed * deltaTime
    }
}

// MARK: - Particle Emitter Extensions

extension ParticleEmitter {

    /**
     * 庆祝粒子发射器
     */
    static func celebration(at position: CGPoint) -> ParticleEmitter {
        return ParticleEmitter(
            emissionPosition: position,
            emissionRate: 20,
            maxParticles: 60,
            continuousEmission: false,
            particleType: .star,
            colors: [
                Color(hex: "#ff6b6b"),
                Color(hex: "#feca57"),
                Color(hex: "#48dbfb"),
                Color(hex: "#ff9ff3"),
                Color(hex: "#54a0ff")
            ],
            sizeRange: 6...12,
            velocityRange: 100...250,
            angleRange: (-Double.pi/4)...(Double.pi/4),
            lifespanRange: 1.5...3.0,
            gravity: 150,
            airResistance: 0.95,
            fadeIn: true,
            fadeOut: true,
            scaleOverLife: true,
            rotationSpeed: 180
        )
    }

    /**
     * 火花粒子发射器
     */
    static func sparkle(at position: CGPoint) -> ParticleEmitter {
        return ParticleEmitter(
            emissionPosition: position,
            emissionRate: 15,
            maxParticles: 45,
            continuousEmission: false,
            particleType: .sparkle,
            colors: [
                Color(hex: "#feca57"),
                Color(hex: "#ff6b6b"),
                Color.white
            ],
            sizeRange: 4...8,
            velocityRange: 80...180,
            angleRange: 0...(2 * .pi),
            lifespanRange: 1.0...2.5,
            gravity: 80,
            airResistance: 0.97,
            fadeIn: false,
            fadeOut: true,
            scaleOverLife: false,
            rotationSpeed: 360
        )
    }

    /**
     * 爆炸粒子发射器
     */
    static func explosion(at position: CGPoint) -> ParticleEmitter {
        return ParticleEmitter(
            emissionPosition: position,
            emissionRate: 30,
            maxParticles: 90,
            continuousEmission: false,
            particleType: .circle,
            colors: [
                Color.orange,
                Color.red,
                Color.yellow
            ],
            sizeRange: 3...10,
            velocityRange: 150...300,
            angleRange: 0...(2 * .pi),
            lifespanRange: 0.8...2.0,
            gravity: 200,
            airResistance: 0.92,
            fadeIn: false,
            fadeOut: true,
            scaleOverLife: true,
            rotationSpeed: 0
        )
    }
}

// MARK: - Particle Shapes

/**
 * 星形粒子形状
 */
struct StarShape: Shape {

    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2
        let innerRadius = radius * 0.4

        let points = 5
        let angleStep = .pi * 2 / Double(points * 2)

        for i in 0..<(points * 2) {
            let angle = Double(i) * angleStep - .pi / 2
            let currentRadius = i % 2 == 0 ? radius : innerRadius
            let x = center.x + CGFloat(cos(angle)) * currentRadius
            let y = center.y + CGFloat(sin(angle)) * currentRadius

            if i == 0 {
                path.move(to: CGPoint(x: x, y: y))
            } else {
                path.addLine(to: CGPoint(x: x, y: y))
            }
        }

        path.closeSubpath()
        return path
    }
}

/**
 * 心形粒子形状
 */
struct HeartShape: Shape {

    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let size = min(rect.width, rect.height)

        let heartWidth = size * 0.8
        let heartHeight = size * 0.7

        // 心形路径
        path.move(to: CGPoint(x: center.x, y: center.y + heartHeight * 0.3))

        path.addCurve(
            to: CGPoint(x: center.x - heartWidth * 0.5, y: center.y - heartHeight * 0.2),
            control1: CGPoint(x: center.x - heartWidth * 0.3, y: center.y + heartHeight * 0.1),
            control2: CGPoint(x: center.x - heartWidth * 0.5, y: center.y)
        )

        path.addCurve(
            to: CGPoint(x: center.x, y: center.y - heartHeight * 0.4),
            control1: CGPoint(x: center.x - heartWidth * 0.5, y: center.y - heartHeight * 0.4),
            control2: CGPoint(x: center.x - heartWidth * 0.2, y: center.y - heartHeight * 0.4)
        )

        path.addCurve(
            to: CGPoint(x: center.x + heartWidth * 0.5, y: center.y - heartHeight * 0.2),
            control1: CGPoint(x: center.x + heartWidth * 0.2, y: center.y - heartHeight * 0.4),
            control2: CGPoint(x: center.x + heartWidth * 0.5, y: center.y - heartHeight * 0.4)
        )

        path.addCurve(
            to: CGPoint(x: center.x, y: center.y + heartHeight * 0.3),
            control1: CGPoint(x: center.x + heartWidth * 0.5, y: center.y),
            control2: CGPoint(x: center.x + heartWidth * 0.3, y: center.y + heartHeight * 0.1)
        )

        return path
    }
}

/**
 * 火花粒子形状
 */
struct SparkleShape: Shape {

    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2

        // 十字形火花
        path.move(to: CGPoint(x: center.x, y: center.y - radius))
        path.addLine(to: CGPoint(x: center.x, y: center.y + radius))
        path.move(to: CGPoint(x: center.x - radius, y: center.y))
        path.addLine(to: CGPoint(x: center.x + radius, y: center.y))

        // 对角线
        let diagonal = radius * 0.7
        path.move(to: CGPoint(x: center.x - diagonal, y: center.y - diagonal))
        path.addLine(to: CGPoint(x: center.x + diagonal, y: center.y + diagonal))
        path.move(to: CGPoint(x: center.x + diagonal, y: center.y - diagonal))
        path.addLine(to: CGPoint(x: center.x - diagonal, y: center.y + diagonal))

        return path
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()

        AdvancedParticleSystemView(
            particleSystem: ParticleSystem(
                emitter: .celebration(at: CGPoint(x: 200, y: 400))
            )
        )
    }
}
