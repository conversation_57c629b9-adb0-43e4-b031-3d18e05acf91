/* 
 * Localizable.strings (Chinese Simplified)
 * ztt2
 * 
 * 转团团应用中文本地化文件
 */

// MARK: - 通用按钮文本
"common.button.cancel" = "取消";
"common.button.confirm" = "确定";
"common.button.delete" = "删除";
"common.button.save" = "保存";
"common.button.close" = "关闭";

// MARK: - 导航和标签页
"tab.home" = "首页";
"tab.diary" = "成长日记";
"tab.profile" = "个人中心";
"tab.current_selection_format" = "当前选中: %d";

// MARK: - 首页（家庭成员管理）
"home.title" = "转团团";
"home.button.add_member" = "添加成员";
"home.button.lottery_config" = "抽奖道具配置";
"home.button.family_operation" = "全员操作";
"home.button.family_total_score" = "全家一共加分";
"home.total_score.title" = "全家一共加分";
"home.member.empty.title" = "暂无成员";
"home.member.empty.description" = "点击\"添加成员\"按钮开始添加成员";

// MARK: - 成员管理
"member.delete.title" = "删除成员";
"member.delete.message" = "确定要删除成员 \"%@\" 吗？";
"member.delete.confirm" = "确定删除";
"member.info.unknown_name" = "未知";
"member.info.default_number" = "000";
"member.info.family_info" = "我的家庭";

// MARK: - 成长日记页面
"growth_diary.title" = "成长日记";
"growth_diary.title.input.placeholder" = "输入日记标题";
"growth_diary.input.placeholder" = "记录今天的成长点滴...";
"growth_diary.button.save" = "保存日记";
"growth_diary.button.view_history" = "查看历史日记";
"growth_diary.date.label" = "记录时间";
"growth_diary.save.success" = "日记保存成功";
"growth_diary.save.error" = "保存失败，请重试";
"growth_diary.input.empty" = "请输入日记内容";

// 选择对象功能
"growth_diary.member.label" = "选择对象";
"growth_diary.member.select" = "选择孩子";
"growth_diary.member.title" = "选择记录对象";
"growth_diary.member.empty" = "还没有添加孩子\n请先在首页添加家庭成员";
"growth_diary.member.picker.title" = "选择记录对象";

// 日期选择器
"growth_diary.date.picker.title" = "选择日期";

// 历史记录
"growth_diary.history.title" = "选择成员";
"growth_diary.history.empty" = "还没有添加孩子\n请先在首页添加家庭成员";

// 报告类型
"growth_diary.report.analysis" = "分析报告";
"growth_diary.report.growth" = "成长报告";
"growth_diary.report.analysis.title" = "积分分析报告";
"growth_diary.report.analysis.description" = "基于积分记录生成的行为分析报告，帮助了解孩子的表现趋势";
"growth_diary.report.growth.title" = "成长日记报告";
"growth_diary.report.growth.description" = "基于日记内容生成的成长分析报告，深入了解孩子的内心世界";

// MARK: - 个人中心页面
"profile.title" = "个人中心";
"profile.user_info.title" = "用户信息";
"profile.user_info.id_label" = "ID:%@";
"profile.user_info.membership_expires" = "会员到期时间：%@";
"profile.subscription.title" = "订阅管理";
"profile.subscription.current_status" = "当前状态";
"profile.subscription.upgrade" = "升级会员";
"profile.subscription.benefits" = "权益说明";
"profile.subscription.banner_text" = "升级会员，解锁更多专属权益";
"profile.subscription.view_plans_button" = "查看会员方案";
"profile.subscription.card_title" = "个人信息卡片";
"profile.no_email" = "未获取到邮箱";
"profile.settings.title" = "系统设置";
"profile.settings.language" = "语言切换";
"profile.settings.history" = "历史生成记录";
"profile.settings.help" = "帮助与反馈";
"profile.settings.about" = "关于";
"profile.settings.delete_account" = "删除账号";
"profile.settings.logout" = "退出登录";

// MARK: - 设置页面
"settings.title" = "设置";
"settings.item.product_introduction" = "产品介绍";
"settings.item.rate_app" = "好评鼓励";
"settings.item.feedback" = "帮助与反馈";
"settings.item.about" = "关于";
"settings.item.clear_all_data" = "清除所有数据";

// MARK: - 自动同步相关
"auto_sync.status.enabled" = "多设备同步已启用";
"auto_sync.status.syncing" = "正在同步数据...";
"auto_sync.status.completed" = "同步完成";
"auto_sync.status.failed" = "同步失败";

"auto_sync.notification.data_synced" = "数据已同步到所有设备";
"auto_sync.notification.settings_synced" = "设置已同步到所有设备";
"auto_sync.notification.sync_failed" = "同步失败，请检查网络连接";

"auto_sync.info.title" = "多设备自动同步";
"auto_sync.info.description" = "您的数据和设置将自动同步到所有登录相同iCloud账户的设备，无需手动操作";
"auto_sync.info.benefits" = "• 数据实时同步\n• 设置跨设备共享\n• 无需手动操作\n• 安全可靠";

"auto_sync.error.no_icloud" = "未登录iCloud账户，请在系统设置中登录以启用同步";
"auto_sync.error.network" = "网络连接异常，同步功能暂时不可用";
"auto_sync.error.storage_full" = "iCloud存储空间不足，请清理后重试";

// MARK: - 订阅相关
"subscription_upgrade.notification.message" = "恭喜！您已成功订阅会员，现在可以享受更多专属功能了。";
"subscription_upgrade.notification.action" = "立即体验";

// MARK: - 功能权限标识
"feature.premium_required" = "会员";
"feature.premium_feature" = "会员功能";
"feature.upgrade_to_unlock" = "升级解锁";

// MARK: - 帮助与反馈
"feedback.contact_email.title" = "联系我们";
"feedback.contact_email.message" = "联系邮箱：<EMAIL>";
"feedback.contact_email.confirm" = "确定";

// MARK: - 应用评分
"rate_app.title" = "好评鼓励";
"rate_app.message" = "如果您喜欢转团团，请在App Store给我们一个好评，这对我们非常重要！";
"rate_app.rate_now" = "去评分";
"rate_app.later" = "稍后提醒";
"rate_app.no_thanks" = "不了，谢谢";

// MARK: - 退出登录
"logout.title" = "退出登录";
"logout.message" = "确定要退出登录吗？";
"logout.cancel" = "取消";
"logout.confirm" = "确认退出";

// MARK: - 删除账号功能
"account_deletion.title" = "删除账号";
"account_deletion.subtitle" = "永久删除您的账号和所有数据";
"account_deletion.warning.title" = "⚠️ 重要提示";
"account_deletion.warning.message" = "删除账号后，您的所有数据将永久丢失且无法恢复，包括：\n\n• 所有班级和学生信息\n• 积分记录和兑换记录\n• 抽奖记录和历史数据\n• 订阅信息\n\n此操作不可逆转，请谨慎操作。";
"account_deletion.confirm_button" = "确认删除";
"account_deletion.cancel_button" = "取消";

// MARK: - 订阅相关
"subscription.membership.basic" = "初级会员";
"subscription.membership.premium" = "高级会员";
"subscription.membership.premium_trial" = "高级会员（试用中）";
"subscription.user_level_regular" = "普通用户";
"subscription.not_activated" = "未开通";

// MARK: - 试用功能
"trial.banner.available_text" = "你有一个限时福利可领取";
"trial.button.claim" = "立即领取";
"trial.status.available" = "30天高级会员试用等你领取";
"trial.status.active" = "试用剩余 %d 天";
"trial.status.expired" = "试用已过期";
"trial.error.already_claimed" = "您已经领取过试用";
"trial.error.claim_failed" = "试用领取失败，请稍后重试";

// MARK: - 试用弹窗
"trial.modal.title" = "限时福利";
"trial.modal.message" = "亲爱的家长，感谢您对孩子教育的关注，我们将赠送您一个月的高级会员，祝您家庭幸福！";
"trial.modal.benefits_title" = "试用期间您将享受：";
"trial.modal.benefit_1" = "创建最多5个班级";
"trial.modal.benefit_2" = "解锁所有抽奖道具";
"trial.modal.benefit_3" = "AI智能分析报告";
"trial.modal.benefit_4" = "配置更多的班级常用规则";
"trial.modal.claim_button" = "立即领取";

// MARK: - 试用期订阅提醒
"trial.reminder.title" = "温馨提醒";
"trial.reminder.message" = "现在订阅会提前结束试用，建议试用期结束后再订阅";
"trial.reminder.confirm" = "谢谢提醒";

// MARK: - 订阅成功消息
"subscription.trial_success_message" = "恭喜！您已成功领取30天高级会员试用！";

// MARK: - 用户信息
"user_info.parent_nickname" = "家长";

// MARK: - 占位页面文本
"placeholder.home.title" = "首页";
"placeholder.diary.title" = "成长日记";
"placeholder.profile.title" = "个人中心";
"placeholder.developing" = "功能开发中...";

// MARK: - 错误信息
"error.network" = "网络连接失败";
"error.unknown" = "未知错误";
"error.permission_denied" = "权限不足";

// MARK: - 成员详情页面
"member_detail.title" = "成员详情";
"member_detail.action.add_points" = "加分";
"member_detail.action.deduct_points" = "扣分";
"member_detail.action.exchange" = "兑换";
"member_detail.action.lottery" = "抽奖";
"member_detail.action.analysis" = "分析";
"member_detail.history.points" = "积分记录";
"member_detail.history.exchange" = "兑换记录";
"member_detail.history.empty.title" = "暂无记录";
"member_detail.history.empty.description" = "学生的积分变动记录将在此显示";
"member_detail.info.role_age_format" = "%@ · %d岁";

// MARK: - 日期范围选择
"date_range.this_week" = "本周";
"date_range.this_month" = "本月";
"date_range.custom" = "自定义";
"date_range.select_range" = "选择时间范围";
"date_range.start_date" = "开始日期";
"date_range.end_date" = "结束日期";
"date_range.invalid_range" = "结束日期不能早于开始日期";
"date_range.total_score_format" = "%@全家一共加分";

// MARK: - 抽奖功能
"lottery.menu.title" = "选择抽奖道具";
"lottery.menu.wheel" = "大转盘";
"lottery.menu.wheel_description" = "转动轮盘获得随机奖品";
"lottery.menu.blind_box" = "盲盒";
"lottery.menu.blind_box_description" = "开启盲盒发现惊喜";
"lottery.menu.scratch_card" = "刮刮卡";
"lottery.menu.scratch_card_description" = "刮开卡片揭晓奖品";

// MARK: - 盲盒功能
"blind_box.title" = "盲盒开箱";
"blind_box.back_button" = "返回";
"blind_box.loading" = "加载中...";
"blind_box.no_config_title" = "暂无盲盒配置";
"blind_box.no_config_message" = "请先配置盲盒奖品和数量";
"blind_box.configure_button" = "去配置";

// MARK: - 盲盒统计
"blind_box.stats.total" = "总数";
"blind_box.stats.unopened" = "未开启";
"blind_box.stats.opened" = "已开启";
"blind_box.stats.cost_per_open" = "每次消耗";

// MARK: - 盲盒状态
"blind_box.status.unopened" = "待开启";
"blind_box.status.opened" = "已开启";
"blind_box.item.title_format" = "盲盒 %d";

// MARK: - 盲盒结果
"blind_box.result.congratulations" = "恭喜获得";
"blind_box.result.confirm" = "确认";
"blind_box.result.mystery_text" = "神秘奖品";
"blind_box.result.congratulations_text" = "🎉 恭喜获得 🎉";
"blind_box.result.cost_label_text" = "消耗积分";
"blind_box.result.obtained_label_text" = "已获得";
"blind_box.result.confirm_button_text" = "确认领取";

// MARK: - 盲盒错误提示
"blind_box.error.insufficient_points_title" = "积分不足";
"blind_box.error.insufficient_points_message" = "开启盲盒需要 %d 积分\n当前积分: %d";
"blind_box.error.understand" = "知道了";

// MARK: - 抽奖权限提示
"lottery.permission.wheel_title" = "大转盘需要初级会员";
"lottery.permission.wheel_message" = "升级到初级会员即可使用大转盘功能";
"lottery.permission.advanced_title" = "高级功能需要高级会员";
"lottery.permission.advanced_message" = "升级到高级会员即可使用盲盒和刮刮卡功能";

// MARK: - 订阅相关
"subscription.upgrade" = "升级会员";
"common.cancel" = "取消";

// MARK: - 抽奖道具配置
"lottery_config.title" = "抽奖道具配置";
"lottery_config.wheel.title" = "大转盘配置";
"lottery_config.blindbox.title" = "盲盒配置";
"lottery_config.scratchcard.title" = "刮刮卡配置";

// 通用配置项
"lottery_config.cost_per_play" = "每次消耗积分";
"lottery_config.cost_per_play.placeholder" = "请输入积分";
"lottery_config.item_count" = "数量设置";
"lottery_config.prizes" = "奖品设置";

// 大转盘配置
"lottery_config.wheel.sector_count" = "分区数量";
"lottery_config.wheel.sector_count.range" = "4-12个分区";
"lottery_config.wheel.sector_format" = "分区 %d";
"lottery_config.wheel.sector_prize" = "分区奖品";

// 盲盒配置
"lottery_config.blindbox.count" = "盲盒数量";
"lottery_config.blindbox.count.range" = "2-20个盲盒";
"lottery_config.blindbox.item_format" = "盲盒 %d";
"lottery_config.blindbox.prize" = "盲盒奖品";

// 刮刮卡配置
"lottery_config.scratchcard.count" = "刮刮卡数量";
"lottery_config.scratchcard.count.range" = "2-20张刮刮卡";
"lottery_config.scratchcard.item_format" = "刮刮卡 %d";
"lottery_config.scratchcard.prize" = "刮刮卡奖品";

// 配置验证
"lottery_config.validation.empty_prize" = "请填写所有奖品名称";
"lottery_config.validation.invalid_cost" = "请输入有效的积分数值";
"lottery_config.validation.cost_range" = "积分必须大于0";
"lottery_config.validation.prize_too_long" = "奖品名称不能超过20个字符";

// 配置操作
"lottery_config.button.save" = "保存配置";
"lottery_config.button.cancel" = "取消";
"lottery_config.save.success" = "配置保存成功";
"lottery_config.save.error" = "保存失败，请重试";

// 错误提示
"lottery_config.error.no_member_selected" = "请先选择成员";

// 刮刮卡配置成功提示
"lottery_config.scratchcard.save_success" = "已为 %@ 保存刮刮卡配置：%d张刮刮卡，每次消耗%d积分";
"lottery_config.scratchcard.save_failed" = "刮刮卡配置保存失败，请检查输入数据";

// MARK: - 大转盘功能
"lottery_wheel.page_title" = "大转盘";
"lottery_wheel.back_button" = "返回";
"lottery_wheel.current_points" = "当前积分：%d";
"lottery_wheel.cost_info" = "每次抽奖消耗 %d 积分";
"lottery_wheel.affordable" = "可抽奖";
"lottery_wheel.insufficient_points" = "积分不足";
"lottery_wheel.spinning" = "转盘旋转中...";
"lottery_wheel.spin_button" = "开始抽奖";
"lottery_wheel.confirm_result" = "确认结果";
"lottery_wheel.mystery_prize" = "神秘奖品";
"lottery_wheel.empty_prize" = "空奖品";

// 大转盘状态提示
"lottery_wheel.no_config.title" = "未配置大转盘";
"lottery_wheel.no_config.description" = "请先配置大转盘奖品后再进行抽奖";
"lottery_wheel.go_settings" = "前往设置";
"lottery_wheel.empty_prizes.title" = "暂无奖品";
"lottery_wheel.empty_prizes.description" = "请先添加奖品后再进行抽奖";

// 积分不足提示
"lottery_wheel.insufficient_points_title" = "积分不足";
"lottery_wheel.insufficient_points_message" = "抽奖需要 %@ 积分，您当前只有 %@ 积分";

// MARK: - 刮刮卡功能
"scratch_card.title" = "刮刮卡";
"scratch_card.remaining_format" = "剩余：%d 张";
"scratch_card.cost_format" = "每张：%d 积分";
"scratch_card.current_points_format" = "当前积分：%d";
"scratch_card.points_sufficient" = "积分充足";
"scratch_card.points_insufficient" = "积分不足";
"scratch_card.loading" = "加载刮刮卡配置中...";
"scratch_card.no_config" = "暂无刮刮卡配置";
"scratch_card.config_description" = "请前往设置页面配置刮刮卡奖品";
"scratch_card.go_settings" = "前往设置";
"scratch_card.back" = "返回";
"scratch_card.happy_title" = "开心刮刮卡";
"scratch_card.happy_subtitle" = "用手刮开涂层，发现惊喜奖品";
"scratch_card.hint_text" = "刮一刮";
"scratch_card.congratulations" = "恭喜中奖！";
"scratch_card.big_reveal" = "刮刮卡大奖揭晓";
"scratch_card.points_consumed_format" = "消耗积分：%d";
"scratch_card.confirm_claim" = "确认领取";
"scratch_card.insufficient_points_title" = "积分不足";
"scratch_card.insufficient_points_message_format" = "刮刮卡需要 %d 积分，当前积分：%d";

// MARK: - 刮刮卡状态
"scratch_card.status.available" = "可刮除";
"scratch_card.status.scratched" = "已刮开";
"scratch_card.status.selected" = "选中";
"scratch_card.status.scratching" = "刮除中";
"scratch_card.status.revealing" = "显示奖品";
"scratch_card.status.completed" = "已完成";
"scratch_card.status.prize_reveal" = "发现惊喜";
"scratch_card.status.won" = "已中奖";
"scratch_card.status.congratulations" = "恭喜获奖";
"scratch_card.status.waiting" = "等待中";

// MARK: - AI分析功能
"ai_analysis.title" = "AI分析";
"ai_analysis.behavior_analysis" = "行为分析报告";
"ai_analysis.growth_report" = "成长报告";
"ai_analysis.button.generate_behavior" = "生成行为分析报告";
"ai_analysis.button.generate_growth" = "生成成长报告";
"ai_analysis.subtitle.behavior" = "基于积分记录分析行为趋势";
"ai_analysis.subtitle.growth" = "基于成长日记生成教育建议";
"ai_analysis.history.title" = "历史报告";
"ai_analysis.history.empty" = "暂无历史报告";
"ai_analysis.history.empty.message" = "还没有生成过AI分析报告\n快去生成第一份报告吧！";
"ai_analysis.current_report" = "最新生成的报告";
"ai_analysis.view_detail" = "点击查看详细内容";
"ai_analysis.view_all_history" = "查看所有历史报告";
"ai_analysis.report_count_format" = "共%d份报告";
"ai_analysis.usage_remaining_format" = "今日剩余：%d/2次";
"ai_analysis.data_stats.behavior_records" = "行为记录";
"ai_analysis.data_stats.growth_diary" = "成长日记";
"ai_analysis.data_stats.min_required_format" = "需要%d条";
"ai_analysis.network_unavailable" = "网络不可用";

// AI分析状态信息
"ai_analysis.generating" = "正在生成AI分析报告";
"ai_analysis.please_wait" = "请稍候，这可能需要几分钟时间...";
"ai_analysis.permission_title" = "您的权限不足";
"ai_analysis.return" = "返回";
"ai_analysis.generate_button" = "生成分析报告";
"ai_analysis.select_report_type" = "选择报告类型";
"ai_analysis.no_network_connection" = "网络连接不可用";

// AI分析错误信息
"ai_analysis.error.no_user" = "用户信息不存在";
"ai_analysis.error.not_premium" = "AI分析功能需要高级会员权限";
"ai_analysis.error.insufficient_records" = "需要至少10条记录才能生成分析报告（当前：%d条）";
"ai_analysis.error.insufficient_point_records" = "积分记录数量不足，需要至少10条积分记录才能生成行为分析报告（当前：%d条）";
"ai_analysis.error.insufficient_growth_diaries" = "成长日记数量不足，需要至少10篇成长日记才能生成成长报告（当前：%d条）";
"ai_analysis.error.no_network" = "网络连接不可用，请检查网络设置";
"ai_analysis.error.rate_limit" = "今日分析次数已用完（2次/天）";
"ai_analysis.error.api_key_missing" = "API密钥未设置";
"ai_analysis.error.api_key_invalid" = "API密钥无效";
"ai_analysis.error.generation_failed" = "生成报告失败";

// AI分析成功信息
"ai_analysis.success.behavior_generated" = "行为分析报告生成成功！";
"ai_analysis.success.growth_generated" = "成长报告生成成功！";

// AI分析权限提示
"ai_analysis.permission.title" = "权限不足";
"ai_analysis.permission.message" = "AI分析功能需要高级会员权限，是否立即升级？";
"ai_analysis.permission.upgrade" = "升级会员";

// AI报告详情
"ai_report.detail.title" = "AI分析报告";
"ai_report.detail.analysis_target" = "分析对象";
"ai_report.detail.analysis_content" = "分析内容";
"ai_report.detail.return" = "返回";
"ai_report.detail.share" = "分享";
"ai_report.detail.copy_report" = "复制报告";
"ai_report.detail.share_report" = "分享报告";
"ai_report.detail.report_copied" = "报告已复制到剪贴板";
"ai_report.detail.usage_tips" = "使用提示";
"ai_report.detail.tip_text_selection" = "长按文本可进行选择和复制";
"ai_report.detail.tip_ai_reference" = "AI生成内容仅供参考，请结合实际情况使用";
"ai_report.detail.generated_time" = "生成时间";
"ai_report.detail.report_type" = "报告类型";
"ai_report.detail.status_completed" = "已完成";
"ai_report.detail.ai_analysis_content" = "AI分析内容";
"ai_report.detail.professional_analysis_desc" = "基于数据生成的专业分析报告";
"ai_report.detail.share" = "分享";
"ai_report.detail.close" = "关闭";
"ai_report.detail.description.behavior" = "基于积分记录数据，运用行为分析学理论生成的专业分析报告";
"ai_report.detail.description.growth" = "基于成长日记内容，运用儿童心理学理论生成的专业成长报告";

// AI报告历史
"ai_report.history.title" = "历史报告";
"ai_report.history.filter.all" = "全部";
"ai_report.history.filter.behavior" = "行为分析";
"ai_report.history.filter.growth" = "成长报告";
"ai_report.history.empty.behavior" = "还没有生成过行为分析报告\n快去生成第一份报告吧！";
"ai_report.history.empty.growth" = "还没有生成过成长报告\n快去生成第一份报告吧！";
"ai_report.history.date.today" = "今天";
"ai_report.history.date.yesterday" = "昨天";

// MARK: - 成功信息
"success.save" = "保存成功";
"success.delete" = "删除成功";
"success.update" = "更新成功";

// MARK: - 订阅页面
"subscription.title" = "会员订阅";
"subscription.user_level_regular" = "普通用户";
"subscription.user_level_basic" = "初级会员";
"subscription.user_level_premium" = "高级会员";
"subscription.not_activated" = "未开通";

// 会员类型
"subscription.membership.basic" = "初级会员";
"subscription.membership.premium" = "高级会员";

// 价格类型
"subscription.pricing.monthly" = "月会员";
"subscription.pricing.yearly" = "年会员";

// 会员权益
"subscription.feature.manage_classes_two" = "成长日记功能";
"subscription.feature.unlock_wheel" = "解锁大转盘道具";
"subscription.feature.multi_device_sync" = "多设备数据同步";
"subscription.feature.basic_functions" = "基础功能使用";
"subscription.feature.all_basic_benefits" = "包含初级会员所有权益";
"subscription.feature.manage_classes_five" = "解锁盲盒道具";
"subscription.feature.unlock_box_scratch" = "解锁刮刮卡道具";
"subscription.feature.ai_reports" = "AI分析报告";

// 订阅按钮
"subscription.subscribe_button" = "立即订阅";

// 服务协议
"subscription.agreement_prompt" = "我已阅读并同意";
"subscription.agreement_link" = "《会员服务协议》";

// 购买流程
"purchase.processing" = "处理中...";
"purchase.success" = "订阅成功";
"purchase.success_processing" = "订阅成功，正在处理...";
"purchase.agreement.reminder" = "请先同意服务协议";
"purchase.error.general" = "购买失败，请重试";
"purchase.error.network" = "网络错误，请检查网络连接";

// 用户信息格式化
"profile.user_info.id_label" = "ID: %@";
"profile.user_info.membership_expires" = "会员到期: %@";

// 选项卡当前选择
"tab.current_selection" = "当前选中: %@";

// MARK: - 关于页面
"settings.item.user_agreement" = "用户服务协议";
"settings.item.privacy_policy" = "隐私政策";
"settings.item.children_privacy_policy" = "儿童个人隐私保护政策";

// MARK: - 登录页面协议
"login.agreement.prefix" = "我已阅读并同意";
"login.agreement.user_agreement" = "《用户服务协议》";
"login.agreement.privacy_policy" = "《隐私政策》";
"login.agreement.children_privacy_policy" = "《儿童个人隐私保护政策》";
"login.agreement.and" = "和";
